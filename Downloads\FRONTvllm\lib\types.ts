export type Node = {
  id: string
  x: number
  y: number
  width: number
  height: number
  prompt: string
  imageUrl?: string
  isGenerating: boolean
}

export type Edge = {
  id: string
  from: string
  to: string
}

export type Frame = {
  id: string
  x: number
  y: number
  width: number
  height: number
  prompt: string
  imageUrl?: string
  isGenerating: boolean
}

// Simplified ChatMessage for UI-only demo
export type ChatMessage = {
  id: string
  sender: "user" | "ai"
  text: string
  timestamp: number
}

// --- SANDBOX TYPES ---

// Represents a file or directory in the sandbox filesystem
export type FileSystemNode = {
  id: string
  name: string
  type: "file" | "directory"
  children?: FileSystemNode[]
  content?: string // Only for files
}

// Represents the entire state of a sandbox instance
export type SandboxState = {
  sandboxId: string
  status: "running" | "stopped" | "error"
  previewUrl: string
  terminalOutput: string[]
  fileSystem: FileSystemNode[]
}




