"use client"

import { But<PERSON> } from "@/components/ui/button"
import { PanelRightClose, Terminal } from "lucide-react"

type SecondaryPanelProps = {
  togglePanel: () => void
}

export default function SecondaryPanel({ togglePanel }: SecondaryPanelProps) {
  return (
    <aside className="w-1/2 flex flex-col bg-card border-l relative">
      <Button
        variant="ghost"
        size="icon"
        onClick={togglePanel}
        className="absolute top-2 right-2 z-10 text-muted-foreground hover:bg-secondary hover:text-foreground h-8 w-8"
        title="Close panel"
      >
        <PanelRightClose className="w-5 h-5" />
      </Button>

      <div className="flex flex-col items-center justify-center h-full p-8 text-center">
        <Terminal className="w-16 h-16 text-muted-foreground mb-6" />
        <h3 className="text-xl font-semibold text-foreground mb-4">Secondary Panel</h3>
        <p className="text-muted-foreground text-sm max-w-md">
          This panel is ready for integration with your preferred development environment.
        </p>
        <div className="mt-6 text-xs text-muted-foreground">
          Options: E2B, CodeSandbox, Gitpod, or custom solution
        </div>
      </div>
    </aside>
  )
}
